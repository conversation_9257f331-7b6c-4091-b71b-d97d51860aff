<?php require APPROOT . '/views/includes/header.php'; ?>

<!-- Debug script to help diagnose JavaScript issues -->
<script src="<?php echo BASE_URL; ?>/public/js/debug.js"></script>


<div class="container">
    <?php if (isset($_SESSION['flash_messages']['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['flash_messages']['success']['message']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php unset($_SESSION['flash_messages']['success']); ?>
    <?php endif; ?>
    
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Show Image Editor</h1>
            <p class="text-muted">
                <?php echo $show->name; ?> - <?php echo formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?>
            </p>
        </div>
        <div class="col-md-6 text-end">
            <a href="javascript:history.back();" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Show Details</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <?php
                        // Find the primary image from the images array
                        $primaryImage = null;
                        if (!empty($images)) {
                            foreach ($images as $image) {
                                if (isset($image->is_primary) && $image->is_primary) {
                                    $primaryImage = $image;
                                    break;
                                }
                            }
                            
                            // If no primary image is set but we have images, use the first one
                            if (!$primaryImage && count($images) > 0) {
                                $primaryImage = $images[0];
                            }
                        }
                        
                        if ($primaryImage): 
                            // Use the file_path from the primary image
                            $primaryImagePath = BASE_URL . '/' . $primaryImage->file_path;
                        ?>
                            <img src="<?php echo $primaryImagePath; ?>" class="img-fluid rounded" alt="<?php echo $show->name; ?>">
                        <?php elseif (!empty($show->banner_image)): ?>
                        <?php 
                        // Fallback to banner_image if no primary image found in images array
                        $imagePath = 'uploads/shows/' . $show->banner_image;
                        $thumbnailPath = 'uploads/shows/thumbnails/' . $show->banner_image;
                        $fullThumbnailPath = APPROOT . '/' . $thumbnailPath;
                        $useThumbnail = file_exists($fullThumbnailPath);
                        ?>
                        <img src="<?php echo BASE_URL; ?>/<?php echo $useThumbnail ? $thumbnailPath : $imagePath; ?>" 
                             class="img-fluid rounded" alt="<?php echo $show->name; ?>">
                        <?php else: ?>
                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 150px;">
                            <i class="fas fa-calendar-alt fa-4x text-secondary"></i>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <h5><?php echo $show->name; ?></h5>
                    <p class="text-muted"><?php echo $show->location; ?></p>
                    
                    <div class="mb-2">
                        <strong>Dates:</strong> <?php echo formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?> - <?php echo formatDateTimeForUser($show->end_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?>
                    </div>
                    
                    <div class="mb-2">
                        <strong>Status:</strong> 
                        <?php if (isset($show->status) && $show->status == 'published'): ?>
                            <span class="badge bg-success">Published</span>
                        <?php elseif (isset($show->status) && $show->status == 'completed'): ?>
                            <span class="badge bg-info">Completed</span>
                        <?php elseif (isset($show->status) && $show->status == 'cancelled'): ?>
                            <span class="badge bg-danger">Cancelled</span>
                        <?php else: ?>
                            <span class="badge bg-warning">Draft</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">Image Tools</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="<?php echo BASE_URL; ?>/image_editor/upload/show/<?php echo $show->id; ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-upload me-2"></i> Upload New Images
                        </a>
                        <button type="button" class="list-group-item list-group-item-action" 
                                data-camera-capture="camera-upload" 
                                data-entity-type="show" 
                                data-entity-id="<?php echo $show->id; ?>"
                                data-csrf-token="<?php echo generateCsrfToken(); ?>">
                            <i class="fas fa-camera me-2"></i> Take Photo
                        </button>
                        <a href="<?php echo BASE_URL; ?>/image_editor/browse/show/<?php echo $show->id; ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-images me-2"></i> Browse All Show Images
                        </a>
                        <a href="<?php echo BASE_URL; ?>/image_editor/crop" class="list-group-item list-group-item-action">
                            <i class="fas fa-crop-alt me-2"></i> Crop Images
                        </a>
                        <a href="<?php echo BASE_URL; ?>/image_editor/resize" class="list-group-item list-group-item-action">
                            <i class="fas fa-expand-arrows-alt me-2"></i> Resize Images
                        </a>
                        <a href="<?php echo BASE_URL; ?>/image_editor/filter" class="list-group-item list-group-item-action">
                            <i class="fas fa-magic me-2"></i> Apply Filters
                        </a>
                        <a href="<?php echo BASE_URL; ?>/image_editor/text" class="list-group-item list-group-item-action">
                            <i class="fas fa-font me-2"></i> Add Text
                        </a>
                        <a href="<?php echo BASE_URL; ?>/image_editor/draw" class="list-group-item list-group-item-action">
                            <i class="fas fa-paint-brush me-2"></i> Draw on Images
                        </a>
                        <a href="<?php echo BASE_URL; ?>/image_editor/optimize" class="list-group-item list-group-item-action">
                            <i class="fas fa-compress-arrows-alt me-2"></i> Optimize Images
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">Tips</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check-circle text-success me-2"></i> Use high-resolution banner images</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i> Create sponsor logo collages</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i> Add text overlays for dates</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i> Include venue photos</li>
                        <li><i class="fas fa-check-circle text-success me-2"></i> Optimize for faster loading</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Show Images</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($images)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> No images have been uploaded for this show yet. 
                        <a href="<?php echo BASE_URL; ?>/image_editor/upload/show/<?php echo $show->id; ?>" class="alert-link">Upload some images</a> to get started.
                    </div>
                    <?php else: ?>
                    <div class="row">
                        <?php foreach ($images as $image): ?>
                        <div class="col-md-4 col-sm-6 mb-4">
                            <div class="card h-100">
                                <?php 
                                // Check if thumbnail exists
                                $thumbnailPath = !empty($image->thumbnail_path) ? $image->thumbnail_path : null;
                                
                                if (empty($thumbnailPath)) {
                                    // Try to determine thumbnail path based on original image path
                                    $pathInfo = pathinfo($image->file_path);
                                    $possibleThumbnailPath = $pathInfo['dirname'] . '/thumbnails/' . $pathInfo['basename'];
                                    $fullThumbnailPath = APPROOT . '/' . $possibleThumbnailPath;
                                    
                                    if (file_exists($fullThumbnailPath)) {
                                        $thumbnailPath = $possibleThumbnailPath;
                                    }
                                }
                                ?>
                                <div class="position-relative">
                                    <img src="<?php echo BASE_URL; ?>/<?php echo $thumbnailPath ? $thumbnailPath : $image->file_path; ?>" 
                                         class="card-img-top" alt="Show Image" style="height: 180px; object-fit: cover;">
                                    <div class="position-absolute top-0 start-0 p-2">
                                        <?php if (isset($image->is_primary) && $image->is_primary): ?>
                                        <span class="badge bg-success"><i class="fas fa-star"></i> Primary</span>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($show->banner_image) && $show->banner_image == $image->file_name): ?>
                                        <span class="badge bg-info ms-1"><i class="fas fa-image"></i> Banner</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title text-truncate"><?php echo $image->file_name; ?></h6>
                                    <p class="card-text small">
                                        <span class="badge bg-info"><?php echo $image->width; ?> x <?php echo $image->height; ?></span>
                                        <span class="badge bg-secondary"><?php echo formatFileSize($image->file_size); ?></span>
                                    </p>
                                    <div class="btn-group w-100">
                                        <a href="<?php echo BASE_URL; ?>/image_editor/edit/<?php echo $image->id; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit me-1"></i> Edit
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" data-bs-boundary="viewport" data-bs-display="static" aria-expanded="false">
                                            <span class="visually-hidden">Toggle Dropdown</span>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/image_editor/crop/<?php echo $image->id; ?>">Crop</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/image_editor/resize/<?php echo $image->id; ?>">Resize</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/image_editor/rotate/<?php echo $image->id; ?>">Rotate</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/image_editor/filter/<?php echo $image->id; ?>">Apply Filter</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/image_editor/text/<?php echo $image->id; ?>">Add Text</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/image_editor/draw/<?php echo $image->id; ?>">Draw on Image</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <a class="dropdown-item <?php echo (isset($image->is_primary) && $image->is_primary) ? 'active' : ''; ?>" 
                                                   href="<?php echo BASE_URL; ?>/image_editor/setPrimary/<?php echo $image->id; ?>/show/<?php echo $show->id; ?>">
                                                    <?php if (isset($image->is_primary) && $image->is_primary): ?>
                                                    <i class="fas fa-check me-1"></i> Primary Image
                                                    <?php else: ?>
                                                    Set as Primary
                                                    <?php endif; ?>
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item <?php echo (!empty($show->banner_image) && $show->banner_image == $image->file_name) ? 'active' : ''; ?>" 
                                                   href="<?php echo BASE_URL; ?>/image_editor/setBanner/<?php echo $image->id; ?>/show/<?php echo $show->id; ?>">
                                                    <?php if (!empty($show->banner_image) && $show->banner_image == $image->file_name): ?>
                                                    <i class="fas fa-check me-1"></i> Banner Image
                                                    <?php else: ?>
                                                    Set as Banner
                                                    <?php endif; ?>
                                                </a>
                                            </li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/image_editor/optimize/<?php echo $image->id; ?>">Optimize</a></li>
                                            <li><a class="dropdown-item text-danger" href="<?php echo BASE_URL; ?>/image_editor/delete/<?php echo $image->id; ?>">Delete</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">Batch Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="<?php echo BASE_URL; ?>/image_editor/upload/show/<?php echo $show->id; ?>" class="btn btn-primary">
                                    <i class="fas fa-upload me-2"></i> Upload Multiple
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <?php if (empty($images)): ?>
                                <button class="btn btn-info disabled" disabled title="No images to optimize">
                                    <i class="fas fa-compress-arrows-alt me-2"></i> Optimize All
                                </button>
                                <?php else: ?>
                                <a href="<?php echo BASE_URL; ?>/image_editor/optimize/batch/show/<?php echo $show->id; ?>" class="btn btn-info" id="optimizeAllBtn">
                                    <i class="fas fa-compress-arrows-alt me-2" id="optimizeIcon"></i> <span id="optimizeText">Optimize All</span>
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <?php if (empty($images)): ?>
                                <button type="button" class="btn btn-danger disabled" disabled title="No images to delete">
                                    <i class="fas fa-trash-alt me-2"></i> Delete All
                                </button>
                                <?php else: ?>
                                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAllModal">
                                    <i class="fas fa-trash-alt me-2"></i> Delete All
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete All Images Modal -->
<div class="modal fade" id="deleteAllModal" tabindex="-1" aria-labelledby="deleteAllModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteAllModalLabel">Confirm Delete All Images</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete all images for this show? This action cannot be undone.</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> This will permanently remove all images associated with this show.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="<?php echo BASE_URL; ?>/image_editor/deleteAll/show/<?php echo $show->id; ?>" class="btn btn-danger">Delete All Images</a>
            </div>
        </div>
    </div>
</div>

<?php
// Helper function to format file size
function formatFileSize($bytes) {
    if ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>

<!-- Optimize All Button Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle Optimize All button
    const optimizeAllBtn = document.getElementById('optimizeAllBtn');
    if (optimizeAllBtn) {
        // Only add event listener if there are images to optimize
        optimizeAllBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Get the href attribute
            const href = this.getAttribute('href');
            
            // Disable the button and show loading state
            this.classList.add('disabled');
            this.setAttribute('aria-disabled', 'true');
            
            // Change the icon to a spinner
            const iconElement = document.getElementById('optimizeIcon');
            iconElement.classList.remove('fa-compress-arrows-alt');
            iconElement.classList.add('fa-spinner', 'fa-spin');
            
            // Change the text
            document.getElementById('optimizeText').textContent = 'Optimizing...';
            
            // Create a toast notification
            const toastContainer = document.createElement('div');
            toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            
            const toastElement = document.createElement('div');
            toastElement.className = 'toast show';
            toastElement.setAttribute('role', 'alert');
            toastElement.setAttribute('aria-live', 'assertive');
            toastElement.setAttribute('aria-atomic', 'true');
            
            const toastHeader = document.createElement('div');
            toastHeader.className = 'toast-header bg-info text-white';
            toastHeader.innerHTML = `
                <i class="fas fa-info-circle me-2"></i>
                <strong class="me-auto">Image Optimization</strong>
                <small>Just now</small>
            `;
            
            const toastBody = document.createElement('div');
            toastBody.className = 'toast-body';
            toastBody.innerHTML = 'Optimizing images. Please wait...';
            
            toastElement.appendChild(toastHeader);
            toastElement.appendChild(toastBody);
            toastContainer.appendChild(toastElement);
            document.body.appendChild(toastContainer);
            
            // Navigate to the URL after a short delay
            setTimeout(() => {
                window.location.href = href;
            }, 500);
        });
    }
});

// Ensure Bootstrap is loaded and initialized properly
document.addEventListener('DOMContentLoaded', function() {
    console.log('Image editor show page: DOM content loaded');
    
    // Direct initialization of dropdowns without relying on bootstrap global
    function initializeDropdownsManually() {
        console.log('Manually initializing dropdowns');
        
        // Get all dropdown toggle buttons
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle, [data-bs-toggle="dropdown"]');
        console.log('Found ' + dropdownToggles.length + ' dropdown toggles');
        
        // Add click handlers to each toggle
        dropdownToggles.forEach(function(toggle) {
            // Make sure it has the right attributes
            if (!toggle.hasAttribute('data-bs-toggle')) {
                toggle.setAttribute('data-bs-toggle', 'dropdown');
            }
            
            // Set boundary to viewport for proper positioning
            toggle.setAttribute('data-bs-boundary', 'viewport');
            toggle.setAttribute('data-bs-display', 'static');
            
            // Add click handler
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Find the dropdown menu
                const dropdownMenu = this.nextElementSibling;
                if (!dropdownMenu || !dropdownMenu.classList.contains('dropdown-menu')) {
                    console.warn('No dropdown menu found for toggle:', this);
                    return;
                }
                
                // Toggle the show class
                const isShown = dropdownMenu.classList.contains('show');
                
                // Close all other dropdowns first
                document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                    if (menu !== dropdownMenu) {
                        menu.classList.remove('show');
                    }
                });
                
                // Toggle this dropdown
                if (isShown) {
                    dropdownMenu.classList.remove('show');
                } else {
                    dropdownMenu.classList.add('show');
                    
                    // Position the dropdown
                    const toggleRect = toggle.getBoundingClientRect();
                    
                    // For split button dropdowns, align to the right edge of the toggle
                    if (toggle.classList.contains('dropdown-toggle-split')) {
                        dropdownMenu.style.left = 'auto';
                        dropdownMenu.style.right = '0';
                    }
                    
                    // Ensure the dropdown doesn't go off-screen
                    setTimeout(function() {
                        const menuRect = dropdownMenu.getBoundingClientRect();
                        
                        if (menuRect.right > window.innerWidth) {
                            dropdownMenu.style.left = 'auto';
                            dropdownMenu.style.right = '0';
                        }
                        
                        if (menuRect.bottom > window.innerHeight) {
                            dropdownMenu.style.top = 'auto';
                            dropdownMenu.style.bottom = '100%';
                        }
                    }, 0);
                }
                
                // Add click handler to close when clicking outside
                if (!isShown) {
                    setTimeout(function() {
                        const closeHandler = function(event) {
                            if (!dropdownMenu.contains(event.target) && !toggle.contains(event.target)) {
                                dropdownMenu.classList.remove('show');
                                document.removeEventListener('click', closeHandler);
                            }
                        };
                        
                        document.addEventListener('click', closeHandler);
                    }, 0);
                }
            });
        });
    }
    
    // Try to use Bootstrap if available, otherwise use manual initialization
    if (typeof bootstrap === 'undefined') {
        console.log('Bootstrap not loaded, using manual dropdown initialization');
        initializeDropdownsManually();
        
        // Also try to load Bootstrap
        const bootstrapScript = document.createElement('script');
        bootstrapScript.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js';
        bootstrapScript.integrity = 'sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN';
        bootstrapScript.crossOrigin = 'anonymous';
        
        bootstrapScript.onload = function() {
            console.log('Bootstrap loaded successfully');
            // Re-initialize with Bootstrap
            if (typeof bootstrap !== 'undefined') {
                console.log('Reinitializing with Bootstrap');
                document.querySelectorAll('.dropdown-toggle').forEach(function(toggle) {
                    new bootstrap.Dropdown(toggle);
                });
            }
        };
        
        document.body.appendChild(bootstrapScript);
    } else {
        console.log('Bootstrap already loaded, initializing dropdowns');
        // Initialize with Bootstrap
        document.querySelectorAll('.dropdown-toggle').forEach(function(toggle) {
            new bootstrap.Dropdown(toggle);
        });
    }
});

// Test PWA initialization
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        if (typeof window.pwaFeatures !== 'undefined') {
            window.pwaFeatures.showNotification('PWA Ready', 'PWA features loaded successfully!', 'success');
        } else {
            alert('PWA features not loaded!');
        }
    }, 2000);
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>