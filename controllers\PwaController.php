<?php
/**
 * PWA Controller for Progressive Web App Features
 * Handles PWA-specific functionality including push notifications, offline sync, and app installation
 */

require_once APPROOT . '/models/NotificationModel.php';

class PwaController {
    private $notificationModel;
    private $vapidKeys;
    
    public function __construct() {
        $this->notificationModel = new NotificationModel();
        $this->initVAPIDKeys();
    }
    
    /**
     * Initialize VAPID keys for push notifications
     */
    private function initVAPIDKeys() {
        // Get VAPID keys from config
        $this->vapidKeys = [
            'publicKey' => defined('VAPID_PUBLIC_KEY') ? VAPID_PUBLIC_KEY : null,
            'privateKey' => defined('VAPID_PRIVATE_KEY') ? VAPID_PRIVATE_KEY : null
        ];
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('[PWA] VAPID keys initialized - Public key available: ' . (!empty($this->vapidKeys['publicKey']) ? 'Yes' : 'No'));
        }
    }
    
    /**
     * Get VAPID public key for client-side subscription
     */
    public function getVapidKey() {
        header('Content-Type: application/json');
        
        if (empty($this->vapidKeys['publicKey'])) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('[PWA] VAPID public key not configured');
            }
            
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'VAPID public key not configured'
            ]);
            return;
        }
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('[PWA] Serving VAPID public key');
        }
        
        echo json_encode([
            'success' => true,
            'publicKey' => $this->vapidKeys['publicKey']
        ]);
    }
    
    /**
     * Subscribe user to push notifications
     */
    public function subscribe() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);

        // More specific error checking for better debugging
        if (!isset($_SESSION['user_id'])) {
            if (DEBUG_MODE) {
                error_log("[PWA] Push subscription failed: User not logged in");
            }
            http_response_code(400);
            echo json_encode(['error' => 'User not logged in', 'code' => 'NOT_LOGGED_IN']);
            return;
        }

        if (!isset($input['subscription'])) {
            if (DEBUG_MODE) {
                error_log("[PWA] Push subscription failed: Invalid subscription data");
            }
            http_response_code(400);
            echo json_encode(['error' => 'Invalid subscription data', 'code' => 'INVALID_SUBSCRIPTION']);
            return;
        }
        
        try {
            $userId = $_SESSION['user_id'];
            $subscription = $input['subscription'];
            $userAgent = $input['userAgent'] ?? '';
            
            // Validate subscription data
            if (!isset($subscription['endpoint']) || 
                !isset($subscription['keys']['p256dh']) || 
                !isset($subscription['keys']['auth'])) {
                throw new Exception('Invalid subscription format');
            }
            
            // Save subscription to database
            $subscriptionId = $this->notificationModel->savePushSubscription(
                $userId,
                $subscription['endpoint'],
                $subscription['keys']['p256dh'],
                $subscription['keys']['auth'],
                $userAgent
            );
            
            if (DEBUG_MODE) {
                error_log("[PWA] Push subscription saved for user {$userId}: {$subscriptionId}");
            }
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'subscriptionId' => $subscriptionId,
                'message' => 'Subscription saved successfully'
            ]);
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Subscription error: " . $e->getMessage());
            }
            
            http_response_code(500);
            echo json_encode([
                'error' => 'Failed to save subscription',
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Handle offline form submissions and sync
     */
    public function offlineSync() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode(['error' => 'User not logged in']);
            return;
        }
        
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $syncItems = $input['items'] ?? [];
            
            $results = [];
            
            foreach ($syncItems as $item) {
                $result = $this->processSyncItem($item);
                $results[] = [
                    'id' => $item['id'] ?? null,
                    'type' => $item['type'] ?? 'unknown',
                    'success' => $result['success'],
                    'message' => $result['message']
                ];
            }
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'results' => $results,
                'synced' => count(array_filter($results, fn($r) => $r['success']))
            ]);
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Offline sync error: " . $e->getMessage());
            }
            
            http_response_code(500);
            echo json_encode([
                'error' => 'Sync failed',
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Process individual sync item
     */
    private function processSyncItem($item) {
        try {
            $type = $item['type'] ?? 'unknown';
            $data = $item['data'] ?? [];
            
            switch ($type) {
                case 'registration':
                    return $this->syncRegistration($data);
                case 'payment':
                    return $this->syncPayment($data);
                case 'scoring':
                    return $this->syncScoring($data);
                default:
                    return ['success' => false, 'message' => 'Unknown sync type'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Sync offline registration
     */
    private function syncRegistration($data) {
        // Validate required fields
        $required = ['show_id', 'vehicle_id'];
        foreach ($required as $field) {
            if (!isset($data[$field])) {
                return ['success' => false, 'message' => "Missing required field: {$field}"];
            }
        }
        
        // Process registration through existing controller
        require_once APPROOT . '/controllers/RegistrationController.php';
        $registrationController = new RegistrationController();
        
        // Simulate POST data
        $_POST = $data;
        
        ob_start();
        $result = $registrationController->processRegistration();
        $output = ob_get_clean();
        
        return ['success' => true, 'message' => 'Registration synced successfully'];
    }
    
    /**
     * Sync offline payment
     */
    private function syncPayment($data) {
        // Validate required fields
        $required = ['amount', 'type'];
        foreach ($required as $field) {
            if (!isset($data[$field])) {
                return ['success' => false, 'message' => "Missing required field: {$field}"];
            }
        }
        
        // Process payment through existing controller
        require_once APPROOT . '/controllers/PaymentController.php';
        $paymentController = new PaymentController();
        
        // Simulate POST data
        $_POST = $data;
        
        ob_start();
        $result = $paymentController->processPayment();
        $output = ob_get_clean();
        
        return ['success' => true, 'message' => 'Payment synced successfully'];
    }
    
    /**
     * Sync offline scoring
     */
    private function syncScoring($data) {
        // Validate required fields
        $required = ['vehicle_id', 'scores'];
        foreach ($required as $field) {
            if (!isset($data[$field])) {
                return ['success' => false, 'message' => "Missing required field: {$field}"];
            }
        }
        
        // Process scoring through existing controller
        require_once APPROOT . '/controllers/JudgeController.php';
        $judgeController = new JudgeController();
        
        // Simulate POST data
        $_POST = $data;
        
        ob_start();
        $result = $judgeController->submitScores();
        $output = ob_get_clean();
        
        return ['success' => true, 'message' => 'Scores synced successfully'];
    }
    
    /**
     * Get cached data for offline use
     */
    public function getCachedData() {
        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode(['error' => 'User not logged in']);
            return;
        }
        
        try {
            $userId = $_SESSION['user_id'];
            $type = $_GET['type'] ?? 'dashboard';
            
            $data = [];
            
            switch ($type) {
                case 'dashboard':
                    $data = $this->getDashboardData($userId);
                    break;
                case 'events':
                    $data = $this->getEventsData($userId);
                    break;
                case 'registrations':
                    $data = $this->getRegistrationsData($userId);
                    break;
                default:
                    $data = ['error' => 'Unknown data type'];
            }
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'data' => $data,
                'cached_at' => gmdate('Y-m-d H:i:s')
            ]);
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Get cached data error: " . $e->getMessage());
            }
            
            http_response_code(500);
            echo json_encode([
                'error' => 'Failed to get cached data',
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Get dashboard data for caching
     */
    private function getDashboardData($userId) {
        require_once APPROOT . '/models/UserModel.php';
        require_once APPROOT . '/models/ShowModel.php';
        require_once APPROOT . '/models/RegistrationModel.php';
        
        $userModel = new UserModel();
        $showModel = new ShowModel();
        $registrationModel = new RegistrationModel();
        
        return [
            'user' => $userModel->getUserById($userId),
            'upcoming_shows' => $showModel->getUpcomingShows(5),
            'recent_registrations' => $registrationModel->getUserRegistrations($userId, 5),
            'notifications' => $this->notificationModel->getUnreadNotifications($userId, 10)
        ];
    }
    
    /**
     * Get events data for caching
     */
    private function getEventsData($userId) {
        require_once APPROOT . '/models/CalendarModel.php';
        
        $calendarModel = new CalendarModel();
        
        return [
            'upcoming_events' => $calendarModel->getUpcomingEvents(20),
            'user_events' => $calendarModel->getUserEvents($userId),
            'featured_events' => $calendarModel->getFeaturedEvents(10)
        ];
    }
    
    /**
     * Get registrations data for caching
     */
    private function getRegistrationsData($userId) {
        require_once APPROOT . '/models/RegistrationModel.php';
        require_once APPROOT . '/models/VehicleModel.php';
        
        $registrationModel = new RegistrationModel();
        $vehicleModel = new VehicleModel();
        
        return [
            'registrations' => $registrationModel->getUserRegistrations($userId),
            'vehicles' => $vehicleModel->getUserVehicles($userId),
            'pending_payments' => $registrationModel->getPendingPayments($userId)
        ];
    }
    
    /**
     * Handle QR code scanning results
     */
    public function processQRScan() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $qrData = $input['qrData'] ?? '';
            
            if (empty($qrData)) {
                throw new Exception('No QR data provided');
            }
            
            // Parse QR code data
            $result = $this->parseQRCode($qrData);
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'type' => $result['type'],
                'action' => $result['action'],
                'data' => $result['data']
            ]);
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] QR scan error: " . $e->getMessage());
            }
            
            http_response_code(500);
            echo json_encode([
                'error' => 'Failed to process QR code',
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Parse QR code data and determine action
     */
    private function parseQRCode($qrData) {
        // Check if it's a URL
        if (filter_var($qrData, FILTER_VALIDATE_URL)) {
            $url = parse_url($qrData);
            $path = $url['path'] ?? '';
            
            if (strpos($path, '/vote/') !== false) {
                return [
                    'type' => 'vote',
                    'action' => 'redirect',
                    'data' => ['url' => $qrData]
                ];
            } elseif (strpos($path, '/registration/') !== false) {
                return [
                    'type' => 'registration',
                    'action' => 'redirect',
                    'data' => ['url' => $qrData]
                ];
            } elseif (strpos($path, '/show/') !== false) {
                return [
                    'type' => 'show',
                    'action' => 'redirect',
                    'data' => ['url' => $qrData]
                ];
            }
        }
        
        // Check if it's JSON data
        $jsonData = json_decode($qrData, true);
        if ($jsonData !== null) {
            return [
                'type' => $jsonData['type'] ?? 'data',
                'action' => $jsonData['action'] ?? 'display',
                'data' => $jsonData
            ];
        }
        
        // Default to text data
        return [
            'type' => 'text',
            'action' => 'display',
            'data' => ['text' => $qrData]
        ];
    }
    
    /**
     * Get PWA installation status and metrics
     */
    public function getInstallMetrics() {
        try {
            // Get installation statistics
            $db = new Database();
            
            $query = "SELECT 
                        COUNT(*) as total_users,
                        COUNT(CASE WHEN last_seen > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_users,
                        COUNT(CASE WHEN user_agent LIKE '%standalone%' THEN 1 END) as installed_users
                      FROM users 
                      WHERE created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)";
            
            $result = $db->query($query);
            $metrics = $result->fetch(PDO::FETCH_ASSOC);
            
            // Get push notification statistics
            $pushQuery = "SELECT 
                            COUNT(*) as total_subscriptions,
                            COUNT(CASE WHEN created_at > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as recent_subscriptions
                          FROM push_subscriptions 
                          WHERE active = 1";
            
            $pushResult = $db->query($pushQuery);
            $pushMetrics = $pushResult->fetch(PDO::FETCH_ASSOC);
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'metrics' => array_merge($metrics, $pushMetrics),
                'generated_at' => gmdate('Y-m-d H:i:s')
            ]);
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Get install metrics error: " . $e->getMessage());
            }
            
            http_response_code(500);
            echo json_encode([
                'error' => 'Failed to get metrics',
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Update user's PWA usage data
     */
    public function updateUsageData() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }
        
        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode(['error' => 'User not logged in']);
            return;
        }
        
        try {
            $userId = $_SESSION['user_id'];
            $input = json_decode(file_get_contents('php://input'), true);
            
            $usageData = [
                'is_installed' => $input['isInstalled'] ?? false,
                'is_standalone' => $input['isStandalone'] ?? false,
                'supports_push' => $input['supportsPush'] ?? false,
                'last_seen' => gmdate('Y-m-d H:i:s'),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ];
            
            $db = new Database();
            
            // Just update the user's last_login to track activity
            $query = "UPDATE users SET last_login = :last_seen WHERE id = :user_id";
            $db->query($query, [
                'last_seen' => $usageData['last_seen'],
                'user_id' => $userId
            ]);
            
            // Log PWA usage data if debug mode is enabled
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('[PWA] Usage data received for user ' . $userId . ': ' . json_encode($usageData));
            }
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'Usage data updated'
            ]);
            
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("[PWA] Update usage data error: " . $e->getMessage());
            }
            
            http_response_code(500);
            echo json_encode([
                'error' => 'Failed to update usage data',
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Handle camera photo upload for direct image editor integration
     */
    public function cameraUpload() {
        // DEBUG START - Output Buffer Debug - REMOVE AFTER TESTING
        // Start output buffering to catch any unexpected output
        ob_start();
        // DEBUG END - Output Buffer Debug
        
        // Enable CORS for PWA
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: POST, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type');
        header('Content-Type: application/json');

        // Handle preflight requests
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit();
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            return;
        }

        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Authentication required']);
            return;
        }

        // Validate CSRF token
        if (!isset($_POST['csrf_token']) || !verifyCsrfToken($_POST['csrf_token'])) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
            return;
        }

        try {
            // DEBUG START - Camera Upload Debugging - REMOVE AFTER TESTING
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("[PWA Camera Upload] POST data: " . json_encode($_POST));
                error_log("[PWA Camera Upload] FILES data: " . json_encode($_FILES));
            }
            // DEBUG END - Camera Upload Debugging
            
            // Validate required fields
            if (!isset($_FILES['image']) || !isset($_POST['entity_type']) || !isset($_POST['entity_id'])) {
                // DEBUG START - Enhanced Error Messages - REMOVE AFTER TESTING
                $missing = [];
                if (!isset($_FILES['image'])) $missing[] = 'image file';
                if (!isset($_POST['entity_type'])) $missing[] = 'entity_type';
                if (!isset($_POST['entity_id'])) $missing[] = 'entity_id';
                throw new Exception('Missing required fields: ' . implode(', ', $missing));
                // DEBUG END - Enhanced Error Messages
                // ORIGINAL CODE: throw new Exception('Missing required fields');
            }

            $entityType = $_POST['entity_type'];
            $entityId = (int)$_POST['entity_id'];
            $uploadedFile = $_FILES['image'];

            // Validate entity type
            $allowedTypes = ['vehicle', 'event', 'show'];
            if (!in_array($entityType, $allowedTypes)) {
                throw new Exception('Invalid entity type');
            }

            // Basic upload error check (ImageEditorModel will handle detailed validation)
            if ($uploadedFile['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('File upload error: ' . $uploadedFile['error']);
            }

            // Verify entity ownership based on type
            $userId = $_SESSION['user_id'];
            
            require_once APPROOT . '/core/Database.php';
            $db = new Database();
            
            if ($entityType === 'vehicle') {
                $db->query("SELECT id FROM vehicles WHERE id = :id AND user_id = :user_id");
                $db->bind(':id', $entityId);
                $db->bind(':user_id', $userId);
                if (!$db->single()) {
                    throw new Exception('Vehicle not found or access denied');
                }
            } elseif ($entityType === 'event') {
                $db->query("SELECT id FROM events WHERE id = :id AND user_id = :user_id");
                $db->bind(':id', $entityId);
                $db->bind(':user_id', $userId);
                if (!$db->single()) {
                    throw new Exception('Event not found or access denied');
                }
            } elseif ($entityType === 'show') {
                $db->query("SELECT id FROM shows WHERE id = :id AND user_id = :user_id");
                $db->bind(':id', $entityId);
                $db->bind(':user_id', $userId);
                if (!$db->single()) {
                    throw new Exception('Show not found or access denied');
                }
            }

            // Use the existing ImageEditorModel for proper upload handling
            require_once APPROOT . '/models/ImageEditorModel.php';
            $imageModel = new ImageEditorModel();
            
            // Use the exact same method as the regular upload
            // This ensures proper file storage, database entries, and system integration
            $uploadDir = 'uploads/' . $entityType . 's/';
            $result = $imageModel->processImageUpload($uploadedFile, $entityType, $entityId, $uploadDir, $userId);

            if ($result === false) {
                throw new Exception('Failed to process image upload');
            }

            // DEBUG START - Success Logging - REMOVE AFTER TESTING
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("[PWA Camera Upload] Successfully uploaded image ID: {$result['id']} for $entityType $entityId using ImageEditorModel");
            }
            // DEBUG END - Success Logging

            echo json_encode([
                'success' => true,
                'message' => 'Image uploaded successfully',
                'image_id' => $result['id'],
                'image_url' => $result['file_path'],
                'filename' => $result['file_name'],
                'thumbnail_url' => $result['thumbnail_path'] ?? null,
                'is_primary' => $result['is_primary'] ?? false
            ]);

        } catch (Exception $e) {
            // DEBUG START - Enhanced Error Handling - REMOVE AFTER TESTING
            // Clean any output buffer
            ob_clean();
            
            // Debug logging
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("[PWA Camera Upload] Error: " . $e->getMessage());
                error_log("[PWA Camera Upload] Stack trace: " . $e->getTraceAsString());
            }
            // DEBUG END - Enhanced Error Handling
            
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        // DEBUG START - PHP Error Handling - REMOVE AFTER TESTING
        } catch (Error $e) {
            // Clean any output buffer
            ob_clean();
            
            // Debug logging for PHP errors
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("[PWA Camera Upload] PHP Error: " . $e->getMessage());
                error_log("[PWA Camera Upload] Stack trace: " . $e->getTraceAsString());
            }
            
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Internal server error'
            ]);
        // DEBUG END - PHP Error Handling
        }
        
        // DEBUG START - Output Buffer Cleanup - REMOVE AFTER TESTING
        // Clean output buffer at the end
        ob_end_clean();
        // DEBUG END - Output Buffer Cleanup
    }
}