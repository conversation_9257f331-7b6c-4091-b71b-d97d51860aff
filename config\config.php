<?php
/**
 * Application Configuration
 */

// Define APPROOT as dirname(FILE) if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(dirname(__FILE__)));
}

// Make APPROOT available globally
if (!isset($GLOBALS['APPROOT'])) {
    $GLOBALS['APPROOT'] = APPROOT;
}

// Database configuration
define('DB_HOST', 'ny-mysql01.offloadsql.com');
define('DB_USER', 'sql24006_forward-limpet');
define('DB_PASS', '80kO3&L%HedJv6eb^');
define('DB_NAME', 'sql24006_events');

// URL and path configuration
define('BASE_URL', 'https://events.rowaneliterides.com');
define('URLROOT', 'https://events.rowaneliterides.com');

// Application settings
define('APP_NAME', 'Rowan Elite Rides Public Events and Shows');
define('APP_VERSION', '3.63.21');
define('INSTALL_DATE', date('Y-m-d H:i:s'));
define('DEBUG_MODE', true); // Enable debug mode

// Facebook OAuth settings
define('FB_APP_ID', '781908744267875');
define('FB_APP_SECRET', '1c412df90fa08950d994c388dba47d7d');
define('FB_REDIRECT_URI', BASE_URL . '/auth/facebookCallback');

// File upload settings
define('UPLOAD_DIR', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif']);

// Security settings
define('CSRF_TOKEN_NAME', 'csrf_token');
//define('SESSION_LIFETIME', 86400); // 24 hours
//define('REMEMBER_ME_LIFETIME', 2592000); // 30 days

// Payment settings
define('DEFAULT_CURRENCY', 'USD');
define('PAYMENT_ENABLED', true);

// Installation status
define('INSTALLED', true);

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Time zone
date_default_timezone_set('UTC');

// VAPID Keys for Push Notifications
define('VAPID_PUBLIC_KEY', 'BEl62iUYgUivxIkv69yViEuiBIa40HI0DLLat3eAALZ-pOSXgNGmhGBgkgGLfCWQkaNwG7x2SkqTuTU4jNuBKPU');
define('VAPID_PRIVATE_KEY', 'J2QqQJ5L8K9M3N4O5P6Q7R8S9T0U1V2W3X4Y5Z6A7B8C');
define('VAPID_SUBJECT', 'https://events.rowaneliterides.com');

// Load environment-specific configuration if exists
$env_config = __DIR__ . '/env.config.php';
if (file_exists($env_config)) {
    require_once $env_config;
}